import styled from "styled-components";

export const Container = styled.section`
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: flex-start;
  flex: 1 0 0;
  gap: 30px;
  align-self: stretch;
  margin-bottom: 40px;
`;

export const FilterSection = styled.div`
  display: flex;
  flex-direction: row;
  gap: 16px;
  padding: 0 70px;
  align-self: stretch;
  justify-content: space-between;
  align-items: center;

  @media (max-width: 980px) {
    padding: 0 36px;
    flex-direction: column;
    align-items: flex-start;
  }
`;

export const CheckboxContainer = styled.div`
  display: flex;
  align-items: center;
  gap: 24px;
`;

export const ReportButton = styled.button`
  display: flex;
  padding: 12px 24px;
  justify-content: center;
  align-items: center;
  gap: 8px;
  border-radius: 50px;
  background: #10265f;
  color: white;
  border: none;
  cursor: pointer;
  font-family: Poppins;
  font-size: 16px;
  font-weight: 600;
  transition: background-color 0.2s;

  &:hover {
    background: #1a3a8f;
  }

  &:focus {
    outline: none;
  }

  &:focus-visible {
    outline: 2px solid #10265f;
    outline-offset: 2px;
  }

  @media (max-width: 980px) {
    font-size: 14px;
  }
`;
