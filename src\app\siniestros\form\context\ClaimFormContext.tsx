'use client';

import { createContext, useContext, ReactNode } from 'react';
import { useClaimForm } from '../hooks/useClaimForm';

type ClaimFormContextType = ReturnType<typeof useClaimForm>;

const ClaimFormContext = createContext<ClaimFormContextType | null>(null);

interface ClaimFormProviderProps {
  children: ReactNode;
}

export const ClaimFormProvider = ({ children }: ClaimFormProviderProps) => {
  const claimFormData = useClaimForm();

  return (
    <ClaimFormContext.Provider value={claimFormData}>
      {children}
    </ClaimFormContext.Provider>
  );
};

export const useClaimFormContext = () => {
  const context = useContext(ClaimFormContext);
  if (!context) {
    throw new Error('useClaimFormContext must be used within a ClaimFormProvider');
  }
  return context;
};