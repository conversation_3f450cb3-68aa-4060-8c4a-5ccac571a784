import { FileText, Trash2, Plus } from "lucide-react";

interface Document {
  id: number;
  nombreDocumento: string;
  descripcion: string;
  obligatorio: boolean;
}

interface DocumentUploadProps {
  documents: File[];
  requiredDocuments: Document[];
  onAddDocument: (file: File) => void;
  onRemoveDocument: (index: number) => void;
  hasError: boolean;
}

const DocumentUpload = ({
  documents,
  requiredDocuments,
  onAddDocument,
  onRemoveDocument,
  hasError,
}: DocumentUploadProps) => {
  console.log("🔄 DocumentUpload render - documents count:", documents.length);
  
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    console.log("🎯 handleFileChange triggered");
    const file = e.target.files?.[0];
    console.log("🎯 Selected file:", file?.name);
    if (file) {
      console.log("🎯 Calling onAddDocument with file:", file.name);
      onAddDocument(file);
      e.target.value = "";
    } else {
      console.log("🎯 No file selected");
    }
  };

  return (
    <div style={{ marginTop: "2rem" }}>
      <div
        style={{
          display: "flex",
          flexDirection: "column",
          marginBottom: "8px",
          width: "100%",
          gap: "5px",
          fontWeight: 600,
        }}
      >
        {requiredDocuments
          .filter((doc) => doc.obligatorio)
          .map((doc) => (
            <div key={doc.id} style={{ marginBottom: "8px" }}>
              <h3 style={{ fontWeight: 500, margin: 0 }}>
                {doc.nombreDocumento}
              </h3>
              <small style={{ fontWeight: 400 }}>{doc.descripcion}</small>
            </div>
          ))}

        {documents && documents.length > 0 && (
          <div style={{ marginBottom: "1rem" }}>
            <h4
              style={{
                margin: "0 0 0.5rem 0",
                fontSize: "0.9rem",
                color: "#374151",
              }}
            >
              Documentos agregados:
            </h4>
            {documents.map((doc, index) => (
              <div
                key={index}
                style={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "space-between",
                  padding: "0.5rem",
                  backgroundColor: "#f8fffe",
                  border: "1px solid #10b981",
                  borderRadius: "8px",
                  marginBottom: "0.5rem",
                }}
              >
                <div
                  style={{
                    display: "flex",
                    alignItems: "center",
                    gap: "0.5rem",
                  }}
                >
                  <FileText size={16} color="#10b981" />
                  <span style={{ fontSize: "0.8rem", color: "#374151" }}>
                    {doc.name}
                  </span>
                </div>
                <button
                  type="button"
                  onClick={() => onRemoveDocument(index)}
                  style={{
                    background: "none",
                    border: "none",
                    cursor: "pointer",
                    padding: "0.2rem",
                    borderRadius: "4px",
                    display: "flex",
                    alignItems: "center",
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.backgroundColor = "#fee2e2";
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.backgroundColor = "transparent";
                  }}
                >
                  <Trash2 size={14} color="#ef4444" />
                </button>
              </div>
            ))}
          </div>
        )}

        <label
          htmlFor="file-upload"
          style={{
            border: `2px dashed ${hasError ? "#ff6b6b" : "#51519b"}`,
            borderRadius: "12px",
            padding: "1rem",
            textAlign: "center",
            backgroundColor: "#fafbff",
            cursor: "pointer",
            transition: "all 0.3s ease",
            position: "relative",
            minHeight: "70px",
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
            justifyContent: "center",
            gap: "0.5rem",
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.backgroundColor = "#f0f2ff";
            e.currentTarget.style.borderColor = "#51519b";
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.backgroundColor = "#fafbff";
            e.currentTarget.style.borderColor = hasError ? "#ff6b6b" : "#51519b";
          }}
        >
          <input
            id="file-upload"
            type="file"
            accept=".pdf,.jpg,.jpeg,.png,.doc,.docx"
            onChange={handleFileChange}
            style={{ display: "none" }}
          />

          <Plus size={20} color="#51519b" />
          <div style={{ color: "#6b7280", fontSize: "0.9rem" }}>
            Agregar documento
          </div>
          <div style={{ color: "#9ca3af", fontSize: "0.8rem" }}>
            PDF, JPG, PNG, DOC, DOCX
          </div>
        </label>
        {hasError && (
          <span style={{ color: "#ff0000", fontSize: "0.8rem" }}>
            Al menos un documento es requerido
          </span>
        )}
      </div>
    </div>
  );
};

export default DocumentUpload;