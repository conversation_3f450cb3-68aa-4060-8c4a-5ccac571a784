"use client";
import React, { useState, useCallback, useEffect } from "react";
import * as S from "./MyClaims.styles";
import { StatusTag, FilterCheckbox, Table, StatusText } from "@/app/components/common";
import { Claim, ClaimStatus, StatusType, StatusTextType } from "@/types/claims";
import { useRouter } from "next/navigation";
import { useClaims } from "@/hooks/useClaims";

const MyClaims = () => {
  const router = useRouter();
  const [showIncomplete, setShowIncomplete] = useState(false);
  const [showFinished, setShowFinished] = useState(false);

  // Hook para manejar claims desde la API
  const {
    claims,
    fetchClaims
  } = useClaims();

  // Función para mapear el estado de reclamación al formato esperado
  const mapClaimStatus = (status: string): ClaimStatus => {
    switch (status.toLowerCase()) {
      case 'propuesta finiquito':
        return 'Propuesta finiquito';
      case 'finiquito':
        return 'Finiquito';
      case 'documentos':
        return 'Documentos';
      case 'proceso de pago':
        return 'Proceso de pago';
      case 'pago':
      case 'procesando pago':
        return 'Pago';
      case 'finalizado':
      case 'completado':
        return 'Finalizado';
      default:
        return 'Documentos'; // Default fallback
    }
  };

  // Función para mapear el estatus de póliza
  const mapPolizaStatus = (status: string): StatusType => {
    switch (status.toLowerCase()) {
      case 'cancelada':
        return 'cancelada';
      case 'vigente':
        return 'Vigente';
      case 'vencida':
        return 'Vencida';
      default:
        return 'Vigente'; // Default fallback
    }
  };

  // Función para mapear StatusType a StatusTextType para el componente StatusText
  const mapStatusTypeToStatusTextType = (status: StatusType): StatusTextType => {
    switch (status) {
      case 'cancelada':
        return 'Rechazado'; // Mapear cancelada a Rechazado para el componente
      case 'Vigente':
        return 'Vigente';
      case 'Vencida':
        return 'Vencida';
      default:
        return 'Vigente';
    }
  };

  // Mapear datos de la API al formato esperado por la tabla
  const allClaims: Claim[] = claims.map(claim => ({
    id: claim.id, // ¡Agregar el ID que faltaba!
    poliza: claim.nombreProducto,
    numeroPoliza: claim.numeroPoliza,
    estatusPoliza: mapPolizaStatus(claim.estatusPoliza),
    periodo: `${claim.vigencia.fechaInicio} al ${claim.vigencia.fechaFin}`,
    sumaAsegurada: `$${claim.planSeleccionado.montoAsegurado}`,
    remanenteSuma: `$${(parseFloat(claim.planSeleccionado.montoAsegurado.replace(/,/g, '')) - parseFloat(claim.totalReclamado.replace(/,/g, ''))).toLocaleString()}`,
    estatusReporte: mapClaimStatus(claim.estatusReclamacion)
  }));

  // Cargar claims al montar el componente
  useEffect(() => {
    fetchClaims({
      page: 1,
      limit: 50,
      sortBy: 'createdAt',
      sortDir: 'desc'
    });
  }, [fetchClaims]);

  const handleStatusClick = useCallback((claimId: number) => {
    router.push(`/siniestros/${claimId}`);
  }, [router]);

  const handleNewReport = useCallback(() => {
    router.push("/siniestros/select");
  }, [router]);

  const filteredClaims = allClaims.filter(claim => {
    if (!showIncomplete && !showFinished) return true;
    if (showIncomplete && ["Documentos", "Finiquito"].includes(claim.estatusReporte)) return true;
    if (showFinished && ["Finalizado", "Pago"].includes(claim.estatusReporte)) return true;
    return false;
  });

  const sortedClaims = [...filteredClaims].reverse();

  return (
    <S.Container>
      <S.FilterSection>
        <S.ReportButton onClick={handleNewReport}>
          Reportar un siniestro
        </S.ReportButton>
        <S.CheckboxContainer>
          <FilterCheckbox
            id="incomplete"
            label="Reportes incompletos"
            checked={showIncomplete}
            onChange={setShowIncomplete}
          />
          <FilterCheckbox
            id="finished"
            label="Reportes finalizados"
            checked={showFinished}
            onChange={setShowFinished}
          />
        </S.CheckboxContainer>
      </S.FilterSection>

      <Table
        data={sortedClaims}
        columns={[
          { header: 'Póliza', accessor: 'poliza' },
          { header: 'Número de póliza', accessor: 'numeroPoliza' },
          {
            header: 'Estatus de la póliza',
            accessor: 'estatusPoliza',
            render: (value) => (
              <StatusText status={mapStatusTypeToStatusTextType(value as StatusType)}>
                {value}
              </StatusText>
            )
          },
          { header: 'Periodo', accessor: 'periodo' },
          { header: 'Suma asegurada', accessor: 'sumaAsegurada' },
          { header: 'Remanente de suma asegurada', accessor: 'remanenteSuma' },
          {
            header: 'Estatus del reporte',
            accessor: 'estatusReporte',
            render: (value, item) => (
              <StatusTag
                status={value as ClaimStatus}
                onClick={() => handleStatusClick(item.id)}
              />
            )
          }
        ]}
        emptyMessage="No se encontraron reportes disponibles."
        aria-label="Mis reportes"
      />
    </S.Container>
  );
};

export default MyClaims;