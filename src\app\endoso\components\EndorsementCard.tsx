interface EndorsementCardProps {
  title: string;
  description: string;
  icon: React.ReactNode;
  onClick?: () => void;
}

const EndorsementCard = ({ title, description, icon, onClick }: EndorsementCardProps) => {
  return (
    <div
      style={{
        backgroundColor: "white",
        border: "2px solid #e2e8f0",
        borderRadius: "16px",
        padding: "32px 24px",
        textAlign: "center",
        cursor: "pointer",
        transition: "all 0.3s ease",
        height: "290px",
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        justifyContent: "center",
        boxShadow: "0 2px 8px rgba(0,0,0,0.04)",
      }}
      onMouseEnter={(e) => {
        e.currentTarget.style.borderColor = "#1e3a8a";
        e.currentTarget.style.transform = "translateY(-4px)";
        e.currentTarget.style.boxShadow = "0 12px 32px rgba(30, 58, 138, 0.15)";
      }}
      onMouseLeave={(e) => {
        e.currentTarget.style.borderColor = "#e2e8f0";
        e.currentTarget.style.transform = "translateY(0)";
        e.currentTarget.style.boxShadow = "0 2px 8px rgba(0,0,0,0.04)";
      }}
      onClick={onClick}
    >
      <div
        style={{
          width: "64px",
          height: "64px",
          backgroundColor: "#1e3a8a",
          borderRadius: "50%",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          marginBottom: "20px",
        }}
      >
        {icon}
      </div>
      <h3
        style={{
          fontSize: "20px",
          fontWeight: "600",
          color: "#1e3a8a",
          marginBottom: "12px",
          lineHeight: "1.3",
        }}
      >
        {title}
      </h3>
      <p
        style={{
          fontSize: "14px",
          color: "#64748b",
          lineHeight: "1.5",
          margin: "0",
        }}
      >
        {description}
      </p>
    </div>
  );
};

export default EndorsementCard;