import { useState, useCallback } from "react";
import { ClaimService } from "@/infrastructure/services/claimService";
import { APIClaimData, ClaimQueryParams, APIClaimResponse } from "@/types/claims";

interface UseClaimsReturn {
  claims: APIClaimData[];
  loading: boolean;
  error: unknown;
  fetchClaims: (params?: ClaimQueryParams) => Promise<void>;
  refetch: () => Promise<void>;
}

export const useClaims = (): UseClaimsReturn => {
  const [claims, setClaims] = useState<APIClaimData[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<unknown>(null);
  const [lastParams, setLastParams] = useState<ClaimQueryParams | undefined>();

  const fetchClaims = useCallback(async (params?: ClaimQueryParams) => {
    setLoading(true);
    setError(null);
    setLastParams(params);

    try {
      const service = new ClaimService();

      // Agregar timeout para evitar carga infinita
      const timeoutPromise = new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Timeout: La carga de claims está tomando demasiado tiempo')), 10000)
      );

      const claimsPromise = service.getClaims(params);

      const response = await Promise.race([claimsPromise, timeoutPromise]) as APIClaimResponse;
      setClaims(response.data || []);
    } catch (err) {
      setError(err);
      console.error('Error fetching claims:', err);
    } finally {
      setLoading(false);
    }
  }, []);

  const refetch = useCallback(async () => {
    await fetchClaims(lastParams);
  }, [fetchClaims, lastParams]);

  return { 
    claims, 
    loading, 
    error, 
    fetchClaims,
    refetch
  };
};
