import { useState } from "react";
import { But<PERSON>, useMediaQuery, Icon<PERSON>utton } from "@mui/material";
import { Edit, Trash2 } from "lucide-react";
import ModalArticle from '../components/forms/addArticle'
import { btnStyles } from "../components/styles";
import { useClaimFormContext } from './context/ClaimFormContext';
import FormField from '../components/forms/FormField';


const TalkAboutForm = () => {
  const isSmallScreen = useMediaQuery("(max-width: 700px)");
  const [open, setOpen] = useState<boolean>(false);
  
  const {
    formData,
    errors,
    editingIndex,
    // selectedClaim,
    // remainingCoverage,
    handleInputChange,
    addArticle,
    editArticle,
    deleteArticle,
    // setEditingIndex,
    calculateTotal,
    calculateRemainingCoverage,
    // isTalkAboutValid
  } = useClaimFormContext();

  const toggleModal = () => {
    setO<PERSON>(!open);
  };

  const handleEditArticle = (index: number) => {
    editArticle(index);
    setOpen(true);
  };

  return (
    <>
      <form>
        <strong style={{ fontSize: '1.7rem' }}>Platícanos ¿Qué sucedió?</strong>
        <p style={{ fontSize: '1.2rem', fontWeight: 500 }}>Recuerda que la información que nos proporciones debe ser igual al Acta del Ministerio Publico</p>

        <div style={{ display: 'flex', gap: isSmallScreen ? '1rem' : '5rem', marginTop: '3rem', justifyContent: 'space-between', flexDirection: isSmallScreen ? 'column' : 'row' }}>
          <FormField
            label="¿Cuándo?"
            error={errors.fecha}
            errorMessage="Este campo es requerido"
          >
            <input
              type="date"
              value={formData.fecha}
              onChange={(e) => handleInputChange('fecha', e.target.value)}
              style={{
                border: `3px solid ${errors.fecha ? '#ff0000' : '#51519b'}`,
                borderRadius: '15px',
                padding: '8px'
              }}
            />
          </FormField>
          <FormField
            label="¿A qué hora?"
            error={errors.hora}
            errorMessage="Este campo es requerido"
          >
            <input
              type="time"
              value={formData.hora}
              onChange={(e) => handleInputChange('hora', e.target.value)}
              style={{
                border: `3px solid ${errors.hora ? '#ff0000' : '#51519b'}`,
                borderRadius: '15px',
                padding: '8px'
              }}
            />
          </FormField>
          <FormField
            label="¿En dónde?"
            error={errors.lugar}
            errorMessage="Este campo es requerido"
          >
            <input
              type="text"
              value={formData.lugar}
              onChange={(e) => handleInputChange('lugar', e.target.value)}
              style={{
                border: `3px solid ${errors.lugar ? '#ff0000' : '#51519b'}`,
                borderRadius: '15px',
                padding: '8px'
              }}
            />
          </FormField>
        </div>
        <FormField
          label="Describe las circunstancias"
          error={errors.descripcion}
          errorMessage="Este campo es requerido"
        >
          <textarea
            value={formData.descripcion}
            onChange={(e) => handleInputChange('descripcion', e.target.value)}
            maxLength={500}
            style={{
              border: `3px solid ${errors.descripcion ? '#ff0000' : '#51519b'}`,
              borderRadius: '15px',
              padding: '8px',
              minHeight: '100px',
              resize: 'none'
            }}
          ></textarea>
        </FormField>
      </form>
      <strong style={{ fontSize: '1.7rem' }}>¿Qué se perdió?</strong>
      {errors.articles && <p style={{ color: '#ff0000', fontSize: '0.9rem', margin: '5px 0' }}>Debes agregar al menos un artículo</p>}
      {calculateRemainingCoverage() < 0 && (
        <p style={{ color: '#ff0000', fontSize: '0.9rem', margin: '5px 0', backgroundColor: '#ffe6e6', padding: '8px', borderRadius: '4px', border: '1px solid #ffcccc' }}>
          ⚠️ El total reclamado excede la suma asegurada disponible por ${Math.abs(calculateRemainingCoverage()).toFixed(2)}
        </p>
      )}

      {formData.articles.length > 0 && (
        <div>
          {formData.articles.map((article, index) => (
            <div key={index} style={{
              padding: '15px',
              border: '1px solid #ddd',
              borderRadius: '8px',
              margin: '5px 0',
              backgroundColor: '#f9f9f9',
              position: 'relative'
            }}>
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <div style={{ flex: 1 }}>
                  <p>{article.cobertura} ${article.valor}</p>
                  <small>Artículo: {article.articulo}</small>
                </div>
                <div style={{ display: 'flex', gap: '5px', flexDirection: 'row' }}>
                  <IconButton
                    size="small"
                    onClick={() => handleEditArticle(index)}
                    sx={{
                      width: '40px',
                      height: '40px',
                      color: '#51519b',
                      '&:hover': {
                        backgroundColor: 'rgba(81, 81, 155, 0.1)',
                        color: '#51519b'
                      }
                    }}
                  >
                    <Edit size={16} />
                  </IconButton>
                  <IconButton
                    size="small"
                    onClick={() => deleteArticle(index)}
                    sx={{
                      width: '40px',
                      height: '40px',
                      color: '#ff4444',
                      '&:hover': {
                        backgroundColor: 'rgba(255, 68, 68, 0.1)',
                        color: '#ff4444'
                      }
                    }}
                  >
                    <Trash2 size={16} />
                  </IconButton>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
      <Button
        variant="contained"
        onClick={() => setOpen(true)}
        sx={{
          ...btnStyles,
          borderRadius: "20px",
          height: "60px",
          minHeight: '60px',
          width: "200px",
        }}
      >
        + Agregar artículo
      </Button>

      <hr />
      <aside>
        <dl>
          <div style={{ display: 'flex', justifyContent: 'space-between', fontSize: '1.1rem', fontWeight: 500, marginTop: '0.5rem' }}>
            <dt>Importe reclamado</dt>
            <dd style={{ fontWeight: 600 }}>${calculateTotal().toFixed(2)}</dd>
          </div>

          <div style={{ display: 'flex', justifyContent: 'space-between', fontSize: '1.1rem', fontWeight: 500, marginTop: '0.5rem' }}>
            <dt>Remanente de suma asegurada</dt>
            <dd style={{ fontWeight: 600, color: calculateRemainingCoverage() < 0 ? '#ff0000' : '#333' }}>
              ${Math.max(0, calculateRemainingCoverage()).toFixed(2)}
              {calculateRemainingCoverage() < 0 && (
                <span style={{ fontSize: '0.8rem', marginLeft: '4px' }}>(Excede por ${Math.abs(calculateRemainingCoverage()).toFixed(2)})</span>
              )}
            </dd>
          </div>

          <div style={{ display: 'flex', justifyContent: 'space-between', fontSize: '1.1rem', fontWeight: 500, marginTop: '0.5rem' }}>
            <dt>Deducible (10%)</dt>
            <dd style={{ fontWeight: 600 }}>${(calculateTotal() * 0.1).toFixed(2)}</dd>
          </div>
        </dl>

        <div style={{ display: 'flex', justifyContent: 'space-between', fontSize: '1.1rem', fontWeight: 600, marginTop: '1rem', color: '#51519b' }}>
          <h4 style={{ margin: 0 }}>Total reclamado:</h4>
          <span>${(calculateTotal() - (calculateTotal() * 0.1)).toFixed(2)}</span>
        </div>
      </aside>
      <ModalArticle
        open={open}
        setOpen={toggleModal}
        onAddArticle={addArticle}
        editingArticle={editingIndex !== null ? formData.articles[editingIndex] : null}
        isEditing={editingIndex !== null}
      />

    </>
  )
}

export default TalkAboutForm;
