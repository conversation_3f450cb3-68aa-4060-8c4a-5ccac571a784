// Tipos para el sistema de Claims (anteriormente Reports)

export type ClaimStatus =
  | 'Documentos'
  | 'Finiquito'
  | 'Propuesta finiquito'
  | 'Proceso de pago'
  | 'Pago'
  | 'Finalizado';

// Tipo para los pasos del timeline
export interface TimelineStep {
  id: string | number;
  label: string;
  status: string;
  isActive: boolean;
  isCompleted: boolean;
}

// Función helper para crear pasos del timeline basados en el estatus de la reclamación
export const createTimelineSteps = (currentStatus: ClaimStatus): TimelineStep[] => {
  const allSteps = [
    { id: 1, label: 'Documentos', status: 'Documentos' },
    { id: 2, label: 'Finiquito', status: 'Finiquito' },
    { id: 3, label: 'Pago', status: 'Pago' },
    { id: 4, label: 'Finalizado', status: 'Finalizado' }
  ];

  // Mapear el orden de los estados
  const statusOrder: Record<ClaimStatus, number> = {
    'Documentos': 0,
    'Finiquito': 1,
    'Propuesta finiquito': 1,
    'Proceso de pago': 2,
    'Pago': 2,
    'Finalizado': 3
  };

  const currentIndex = statusOrder[currentStatus] ?? 0;

  return allSteps.map((step, index) => ({
    ...step,
    isCompleted: index < currentIndex,
    isActive: index === currentIndex
  }));
};

export type StatusType = 
  | 'Vigente' 
  | 'Vencida' 
  | 'cancelada';

export type StatusTextType = 
  'Vigente' | 'Vencida' | 'Aprobado' | 'Pendiente' | 'Rechazado';

// Interfaz para el claim tal como se muestra en la tabla
export interface Claim {
  id: number; // ID numérico del claim
  poliza: string;
  numeroPoliza: string;
  estatusPoliza: StatusType;
  periodo: string;
  sumaAsegurada: string;
  remanenteSuma: string;
  estatusReporte: ClaimStatus; // Mantenemos el nombre por compatibilidad con componentes existentes
}

// Interfaces para la respuesta de la API /v1/claims/me
export interface APIClaimResponse {
  data: APIClaimData[];
  meta: APIClaimMeta;
}

export interface APIClaimData {
  id: number;
  nombreProducto: string;
  numeroPoliza: string;
  estatusPoliza: string;
  vigencia: {
    fechaInicio: string;
    fechaFin: string;
  };
  planSeleccionado: {
    id: number;
    nombre: string;
    total: string;
    montoAsegurado: string;
    deducible: string;
  };
  totalReclamado: string;
  estatusReclamacion: string;
}

export interface APIClaimMeta {
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// Parámetros para la consulta de claims
export interface ClaimQueryParams {
  status?: string;
  page?: number;
  limit?: number;
  sortBy?: string;
  sortDir?: 'asc' | 'desc';
}

// Tipos para filtros
export interface ClaimFilters {
  showIncomplete: boolean;
  showFinished: boolean;
}

// Tipos para documentos de reclamaciones
export interface ClaimDocument {
  id: number;
  nombreDocumento: string;
  pathDocumento: string;
  tipoDocumento: string;
  estatus: string;
  nota: string;
  esObligatorio: boolean;
  envio: string;
  createdAt: string;
  updatedAt: string;
  cobertura: {
    id: number;
    nombre: string;
  };
}

// Respuesta de la API para documentos de reclamaciones
export interface ClaimDocumentsResponse {
  claimId: string;
  totalDocuments: number;
  documents: ClaimDocument[];
}
