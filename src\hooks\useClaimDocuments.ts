/* eslint-disable @typescript-eslint/no-explicit-any */
import { useState, useCallback } from "react";
import { ClaimService } from "@/infrastructure/services/claimService";
import { ClaimDocument, ClaimDocumentsResponse } from "@/types/claims";
import { useSnackbar } from "@/context/SnackbarContext";

interface UseClaimDocumentsReturn {
  documents: ClaimDocument[];
  loading: boolean;
  error: unknown;
  totalDocuments: number;
  fetchClaimDocuments: (claimId: string) => Promise<void>;
  refetch: () => Promise<void>;
  clearDocuments: () => void;
}

export const useClaimDocuments = (): UseClaimDocumentsReturn => {
  const [documents, setDocuments] = useState<ClaimDocument[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<unknown>(null);
  const [totalDocuments, setTotalDocuments] = useState(0);
  const [lastClaimId, setLastClaimId] = useState<string | undefined>();
  const { showError } = useSnackbar();

  const fetchClaimDocuments = useCallback(async (claimId: string) => {
    if (!claimId) {
      showError("ID de reclamación requerido");
      return;
    }

    setLoading(true);
    setError(null);
    setLastClaimId(claimId);

    try {
      const service = new ClaimService();

      // Agregar timeout para evitar carga infinita
      const timeoutPromise = new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Timeout: La carga de documentos está tomando demasiado tiempo')), 10000)
      );

      const documentsPromise = service.getClaimDocuments(claimId);

      const response = await Promise.race([documentsPromise, timeoutPromise]) as ClaimDocumentsResponse;
      
      setDocuments(response.documents || []);
      setTotalDocuments(response.totalDocuments || 0);
    } catch (err: any) {
      setError(err);
      console.error('Error fetching claim documents:', err);
      
      // Mostrar mensaje de error específico según el código de estado
      if (err?.response?.status === 400) {
        showError("Datos de la reclamación inválidos");
      } else if (err?.response?.status === 401) {
        showError("Token no proporcionado o inválido");
      } else if (err?.response?.status === 403) {
        showError("No tienes permisos para ver los documentos de esta reclamación");
      } else if (err?.response?.status === 404) {
        showError("Reclamación no encontrada");
      } else if (err.message?.includes('Timeout')) {
        showError("La carga de documentos está tomando demasiado tiempo. Intenta nuevamente.");
      } else {
        showError("Error al cargar los documentos de la reclamación");
      }
    } finally {
      setLoading(false);
    }
  }, [showError]);

  const refetch = useCallback(async () => {
    if (lastClaimId) {
      await fetchClaimDocuments(lastClaimId);
    }
  }, [fetchClaimDocuments, lastClaimId]);

  const clearDocuments = useCallback(() => {
    setDocuments([]);
    setTotalDocuments(0);
    setError(null);
    setLastClaimId(undefined);
  }, []);

  return {
    documents,
    loading,
    error,
    totalDocuments,
    fetchClaimDocuments,
    refetch,
    clearDocuments,
  };
};
