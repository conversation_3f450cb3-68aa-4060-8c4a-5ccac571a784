'use client';
import React, { useEffect, useState } from 'react';
import { ProtectedLayout } from '@/core/layouts/ProtectedLayout';
import { StyledTitle } from '@/Styles/General.styles';
import styled from 'styled-components';
import { useRouter } from 'next/navigation';
import useAuth from '@/hooks/useAuth';
import EmptyClaims from './components/EmptyClaims';
import EmptyProducts from './components/EmptyProducts';
import Link from 'next/link';
import { MyClaims } from '../components/Claims';
import { useClaims } from '@/hooks/useClaims';

function SectionHeader({ children }: { children: React.ReactNode }) {
  return (
    <>
      <TitleRow>
        <StyledTitle>Mis reportes</StyledTitle>
        <Link
          href="#"
          style={{
            color: '#6D6D6D',
            fontWeight: 500,
            textDecoration: 'underline',
            fontFamily: 'Poppins',
            fontSize: '18px',
          }}
        >
          Ayuda
        </Link>
      </TitleRow>
      <CenteredContainer>{children}</CenteredContainer>
    </>
  );
}

export default function Siniestros() {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const [myProducts, setMyProducts] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);

  const navigate = useRouter();
  const { getMyProducts } = useAuth();

  // Hook para obtener claims reales
  const {
    claims,
    loading: claimsLoading,
    fetchClaims
  } = useClaims();
  const fetchData = async () => {
    try {
      setLoading(true);

      // Cargar productos del usuario
      const userData = await getMyProducts();
      setMyProducts(Array.isArray(userData.data) ? userData.data : []);

      // Cargar claims del usuario
      await fetchClaims({
        page: 1,
        limit: 50,
        sortBy: 'createdAt',
        sortDir: 'desc'
      });
    } catch (error) {
      console.error('Error al obtener los datos del usuario:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
  // Mostrar loading mientras se cargan los datos
  if (loading || claimsLoading) {
    return (
      <ProtectedLayout>
        <StyledLayout>
          <div className="containerMain">
            <SectionHeader>
              <div style={{ textAlign: 'center', padding: '2rem' }}>
                Cargando...
              </div>
            </SectionHeader>
          </div>
        </StyledLayout>
      </ProtectedLayout>
    );
  }

  return (
    <ProtectedLayout>
      <StyledLayout>
        <div className="containerMain">
          {/* Si no hay productos contratados, mostrar EmptyProducts */}
          {Array.isArray(myProducts) && myProducts.length === 0 ? (
            <SectionHeader>
              <EmptyProducts onReport={() => navigate.push('/')} />
            </SectionHeader>
          ) : Array.isArray(myProducts) && myProducts.length > 0 && claims.length === 0 ? (
            <SectionHeader>
              <EmptyClaims />
            </SectionHeader>
          ) : Array.isArray(myProducts) && myProducts.length > 0 && claims.length > 0 ? (
            <SectionHeader>
              <MyClaims />
            </SectionHeader>
          ) : null}
        </div>
      </StyledLayout>
    </ProtectedLayout>
  );
}

const StyledLayout = styled.div`
  background-color: #fdfaff;
  width: 100%;

  .containerMain {
    display: flex;
    flex-direction: column;
    gap: 21px;
    width: 100%;
    align-items: flex-start;
    justify-content: start;
    max-width: 120rem;
    margin: 0 auto;
    padding: 46px 60px;
  }
`;

// Componentes styled que no se usan han sido removidos para limpiar el código

const CenteredContainer = styled.div`
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
`;

const TitleRow = styled.div`
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
`;
