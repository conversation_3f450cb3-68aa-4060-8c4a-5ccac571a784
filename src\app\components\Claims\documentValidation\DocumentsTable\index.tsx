import React, { useState } from 'react';
import * as S from './DocumentsTable.styles';
import { Document } from '@/types';
import {
  SaveAlt,
  TaskAlt,
  ErrorOutline,
  AccessTime,
  FileUploadOutlined,
  Visibility,
  ExpandMore,
  ExpandLess,
} from '@mui/icons-material';
import { StatusText } from '@/app/components/common';
import { StatusTextType } from '@/types/claims';

interface DocumentsTableProps {
  documents: Document[];
  onDownloadDocument: (document: Document) => void;
  onShowUploadModal: (document: Document) => void;
  emptyMessage?: string;
}

export function DocumentsTable({
  documents,
  onDownloadDocument,
  onShowUploadModal,
  emptyMessage = 'No hay documentos disponibles',
}: DocumentsTableProps) {
  const [expandedRows, setExpandedRows] = useState<Set<string>>(new Set());

  const toggleRow = (documentId: string) => {
    const newExpandedRows = new Set(expandedRows);
    if (newExpandedRows.has(documentId)) {
      newExpandedRows.delete(documentId);
    } else {
      newExpandedRows.add(documentId);
    }
    setExpandedRows(newExpandedRows);
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "Aprobado":
        return <TaskAlt sx={{ fontSize: 20 }} />;
      case "Rechazado":
        return <ErrorOutline sx={{ fontSize: 20 }} />;
      case "Pendiente":
        return <AccessTime sx={{ fontSize: 20 }} />;
      default:
        return null;
    }
  };

  if (documents.length === 0) {
    return (
      <S.EmptyState>
        <div role="alert">{emptyMessage}</div>
      </S.EmptyState>
    );
  }

  return (
    <S.TableContainer>
      <S.Table>
        <S.TableHeader>
          <S.HeaderRow>
            <S.HeaderCell>PRODUCTO</S.HeaderCell>
            <S.HeaderCell>DOCUMENTO</S.HeaderCell>
            <S.HeaderCell>ESTADO</S.HeaderCell>
            <S.HeaderCell>ACCIONES</S.HeaderCell>
          </S.HeaderRow>
        </S.TableHeader>

        <S.TableBody>
          {documents.map((document) => {
            const isExpanded = expandedRows.has(document.id);
            const hasRejectReason = document.estado === "Rechazado" && document.motivoRechazo;

            return (
              <React.Fragment key={document.id}>
                {/* Fila principal */}
                <S.DataRow>
                  <S.DataCell>
                    <S.ProductName>{document.producto}</S.ProductName>
                  </S.DataCell>

                  <S.DataCell>
                    <S.DocumentName>
                      <S.DocumentIcon $status={document.estado}>
                        {getStatusIcon(document.estado)}
                      </S.DocumentIcon>
                      <span>{document.documento}</span>
                    </S.DocumentName>
                  </S.DataCell>

                  <S.DataCell>
                    <StatusText status={document.estado as StatusTextType}>
                      {document.estado}
                    </StatusText>
                  </S.DataCell>

                  <S.DataCell>
                    <S.ActionButtons>
                      {(document.url || document.archivo) && (
                        <S.ActionButton
                          $variant="secondary"
                          onClick={() => onDownloadDocument(document)}
                        >
                          <SaveAlt sx={{ fontSize: 14 }} />
                          Descargar
                        </S.ActionButton>
                      )}

                      {hasRejectReason && (
                        <S.ActionButton
                          $variant="danger"
                          onClick={() => toggleRow(document.id)}
                        >
                          <Visibility sx={{ fontSize: 14 }} />
                          Ver motivo
                          {isExpanded ? (
                            <ExpandLess sx={{ fontSize: 14 }} />
                          ) : (
                            <ExpandMore sx={{ fontSize: 14 }} />
                          )}
                        </S.ActionButton>
                      )}
                    </S.ActionButtons>
                  </S.DataCell>
                </S.DataRow>

                {/* Fila expandida para mostrar motivo de rechazo */}
                {isExpanded && hasRejectReason && (
                  <S.ExpandedRow>
                    <S.ExpandedCell colSpan={4}>
                      <S.RejectReasonCard>
                        <S.RejectReasonHeader>
                          <S.RejectReasonIcon>
                            <ErrorOutline sx={{ fontSize: 16 }} />
                          </S.RejectReasonIcon>
                          <S.RejectReasonTitle>Motivo del rechazo</S.RejectReasonTitle>
                        </S.RejectReasonHeader>
                        <S.RejectReasonText>
                          {document.motivoRechazo}
                        </S.RejectReasonText>
                        <S.UploadSection>
                          <S.UploadButton onClick={() => onShowUploadModal(document)}>
                            <FileUploadOutlined sx={{ fontSize: 18 }} />
                            Subir nuevo documento
                          </S.UploadButton>
                          <S.UploadText>Formatos permitidos: PDF, JPG, PNG (máx. 5MB)</S.UploadText>
                        </S.UploadSection>
                      </S.RejectReasonCard>
                    </S.ExpandedCell>
                  </S.ExpandedRow>
                )}
              </React.Fragment>
            );
          })}
        </S.TableBody>
      </S.Table>
    </S.TableContainer>
  );
}

export default DocumentsTable;
