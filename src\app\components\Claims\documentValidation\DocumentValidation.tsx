"use client";
import React, { useState, useCallback, useEffect } from "react";
import { useRouter, useParams } from "next/navigation";
import * as S from "./DocumentValidation.styles";
import { Document } from "@/types";
import { Claim, ClaimStatus, TimelineStep, createTimelineSteps, ClaimDocument } from "@/types/claims";
import { Close } from "@mui/icons-material";
import FileUploader from "@/app/components/common/FileUploader";
import { TimelineSteps } from "@/app/components/common";
import DocumentsTable from "./DocumentsTable";
import { ClaimStatusHeader } from "@/app/components/Claims";
import { useClaims } from "@/hooks/useClaims";
import { useClaimDocuments } from "@/hooks/useClaimDocuments";
import { DocumentValidationService } from "@/infrastructure/services/documentValidationService";

const DocumentValidation = () => {
  const router = useRouter();
  const params = useParams();

  // Obtener el ID del claim de la URL
  const claimId = (params?.claimId as string);

  // Estados para modales
  const [showUploadModal, setShowUploadModal] = useState(false);
  const [uploadingDocument, setUploadingDocument] = useState<Document | null>(
    null
  );
  const [selectedFile, setSelectedFile] = useState<File | null>(null);

  // Estados para datos del claim
  const [currentClaim, setCurrentClaim] = useState<Claim | null>(null);

  // Hook para manejar claims desde la API
  const {
    claims,
    loading: claimsLoading,
    fetchClaims
  } = useClaims();

  // Hook para manejar documentos de la reclamación
  const {
    documents: claimDocuments,
    loading: documentsLoading,
    error: documentsError,
    fetchClaimDocuments,
    clearDocuments
  } = useClaimDocuments();

  // Estados para el timeline
  const [timelineSteps, setTimelineSteps] = useState<TimelineStep[]>([]);

  // Cargar datos cuando cambie el ID del claim
  useEffect(() => {
    const loadClaimData = async () => {
      if (!claimId) return;

      try {
        // Resetear estados cuando cambie el claim
        setCurrentClaim(null);
        setTimelineSteps([]);
        clearDocuments();

        // Cargar claims si no están cargados
        if (claims.length === 0) {
          await fetchClaims();
          return; // Salir aquí, el efecto se ejecutará de nuevo cuando se carguen los claims
        }

        // Buscar el claim específico por ID
        const claimData = claims.find(claim => claim.id.toString() === claimId);

        if (claimData) {
          // Función para mapear el estado de reclamación al formato esperado
          const mapClaimStatus = (status: string): ClaimStatus => {
            switch (status.toLowerCase()) {
              case 'propuesta finiquito':
                return 'Propuesta finiquito';
              case 'finiquito':
                return 'Finiquito';
              case 'documentos':
                return 'Documentos';
              case 'proceso de pago':
                return 'Proceso de pago';
              case 'pago':
              case 'procesando pago':
                return 'Pago';
              case 'finalizado':
              case 'completado':
                return 'Finalizado';
              default:
                return 'Documentos'; // Default fallback
            }
          };

          // Función para mapear el estatus de póliza
          const mapPolizaStatus = (status: string) => {
            switch (status.toLowerCase()) {
              case 'cancelada':
                return 'cancelada';
              case 'vigente':
                return 'Vigente';
              case 'vencida':
                return 'Vencida';
              default:
                return 'Vigente'; // Default fallback
            }
          };

          // Mapear los datos de la API al formato esperado por el componente
          const mappedClaim: Claim = {
            id: claimData.id,
            poliza: claimData.nombreProducto,
            numeroPoliza: claimData.numeroPoliza,
            estatusPoliza: mapPolizaStatus(claimData.estatusPoliza),
            periodo: `${claimData.vigencia.fechaInicio} al ${claimData.vigencia.fechaFin}`,
            sumaAsegurada: `$${claimData.planSeleccionado.montoAsegurado}`,
            remanenteSuma: `$${(parseFloat(claimData.planSeleccionado.montoAsegurado.replace(/,/g, '')) - parseFloat(claimData.totalReclamado.replace(/,/g, ''))).toLocaleString()}`,
            estatusReporte: mapClaimStatus(claimData.estatusReclamacion),
          };
          setCurrentClaim(mappedClaim);

          // Crear timeline steps basado en el estatus actual
          const timeline = createTimelineSteps(mappedClaim.estatusReporte);
          setTimelineSteps(timeline);

          // Cargar documentos de la reclamación si el estatus es "Documentos"
          if (mappedClaim.estatusReporte === "Documentos") {
            await fetchClaimDocuments(claimId);
          }
        }
      } catch (error) {
        console.error("Error loading claim data:", error);
      }
    };

    loadClaimData();
  }, [claimId, claims, fetchClaims, fetchClaimDocuments, clearDocuments]);

  const handleBackToReports = useCallback(() => {
    router.push("/siniestros");
  }, [router]);

  const handleDownloadDocument = useCallback(
    async (doc: Document) => {
      try {
        // Intentar usar el servicio de documentos primero
        try {
          const documentService = new DocumentValidationService();
          const blob = await documentService.downloadDocument(doc.id);

          // Crear URL temporal para el blob
          const blobUrl = window.URL.createObjectURL(blob);

          // Crear enlace de descarga
          const link = window.document.createElement("a");
          link.href = blobUrl;
          link.download = `${doc.documento}.pdf`;
          link.style.display = "none";

          // Descargar y limpiar
          window.document.body.appendChild(link);
          link.click();
          window.document.body.removeChild(link);
          window.URL.revokeObjectURL(blobUrl);

          console.log("Documento descargado correctamente:", doc.documento);
          return;
        } catch (serviceError) {
          console.log("Servicio no disponible, usando descarga directa:", serviceError);
        }

        // Fallback: descarga directa si el servicio no funciona
        const documentUrl = doc.url || doc.archivo;

        if (!documentUrl) {
          throw new Error("No se encontró la URL del documento");
        }

        // Si es una URL completa, usarla directamente; si no, construir la URL completa
        const fullUrl = typeof documentUrl === 'string' && documentUrl.startsWith('http')
          ? documentUrl
          : `${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000'}/${documentUrl}`;

        // Crear enlace de descarga directo
        const link = window.document.createElement("a");
        link.href = fullUrl;
        link.download = `${doc.documento}.pdf`;
        link.target = "_blank";
        link.rel = "noopener noreferrer";
        link.style.display = "none";

        window.document.body.appendChild(link);
        link.click();
        window.document.body.removeChild(link);

        console.log("Descarga iniciada correctamente:", doc.documento);
      } catch (error) {
        console.error("Error al descargar documento:", error);
        // Solo mostrar error en casos críticos
        alert("Error al descargar el documento. Inténtalo de nuevo.");
      }
    },
    [] // Sin dependencias para evitar re-renders
  );

  const handleShowUploadModal = useCallback((document: Document) => {
    setUploadingDocument(document);
    setSelectedFile(null); // Limpiar archivo anterior
    setShowUploadModal(true);
  }, []);

  const handleFileUpload = useCallback(
    async (file: File | null) => {
      if (!file || !uploadingDocument) {
        setSelectedFile(null);
        return;
      }

      try {
        // Actualizar el estado del archivo seleccionado
        setSelectedFile(file);

        // TODO: Implementar subida de archivos
        console.log("Upload file:", file, "for document:", uploadingDocument.id);

        // Cerrar modal
        setShowUploadModal(false);
        setUploadingDocument(null);
        setSelectedFile(null);
      } catch (error) {
        console.error("Error al subir archivo:", error);
      }
    },
    [uploadingDocument]
  );

  const handleTimelineStepClick = useCallback((step: TimelineStep) => {
    console.log("Timeline step clicked:", step);
    // Aquí puedes manejar la navegación o acciones específicas del timeline
  }, []);

  // Función para convertir ClaimDocument[] a Document[]
  const convertClaimDocumentsToDocuments = useCallback((claimDocs: ClaimDocument[]): Document[] => {
    return claimDocs.map(doc => ({
      id: doc.id.toString(),
      producto: currentClaim?.poliza || 'N/A',
      categoria: doc.cobertura?.nombre || 'General',
      documento: doc.nombreDocumento,
      estado: doc.estatus as Document['estado'],
      motivoRechazo: doc.nota || undefined,
      fechaCarga: doc.createdAt,
      archivo: doc.pathDocumento,
      url: doc.pathDocumento
    }));
  }, [currentClaim?.poliza]);



  // Mostrar loading state de claims
  if (claimsLoading) {
    return (
      <S.Container>
        <div style={{ textAlign: "center", padding: "2rem" }}>
          Cargando información de la reclamación...
        </div>
      </S.Container>
    );
  }

  return (
    <S.Container>
      <S.HeaderSection>
        <ClaimStatusHeader
          status={currentClaim?.estatusReporte || "Documentos"}
          onBackClick={handleBackToReports}
        />

        {/* Timeline de progreso usando TimelineSteps */}
        <S.TimelineContainer>
          <TimelineSteps
            steps={timelineSteps}
            onStepClick={handleTimelineStepClick}
          />
        </S.TimelineContainer>
      </S.HeaderSection>

      {/* Mostrar tabla de documentos solo cuando el estatus sea "Documentos" */}
      {currentClaim?.estatusReporte === "Documentos" && (
        <S.DocumentsSection>
          <S.SectionHeader>
            <S.SectionTitle>Documentos requeridos</S.SectionTitle>
            <S.SectionSubtitle>
              Revisa el estado de tus documentos y sube los que falten o necesiten
              corrección
            </S.SectionSubtitle>
          </S.SectionHeader>

          {documentsLoading ? (
            <div style={{ textAlign: "center", padding: "2rem" }}>
              Cargando documentos de la reclamación...
            </div>
          ) : documentsError ? (
            <div style={{ textAlign: "center", padding: "2rem", color: "#dc2626" }}>
              Error al cargar los documentos.
              <button
                onClick={() => claimId && fetchClaimDocuments(claimId)}
                style={{ marginLeft: "8px", color: "#3b82f6", textDecoration: "underline", background: "none", border: "none", cursor: "pointer" }}
              >
                Reintentar
              </button>
            </div>
          ) : (
            <DocumentsTable
              documents={convertClaimDocumentsToDocuments(claimDocuments)}
              onDownloadDocument={handleDownloadDocument}
              onShowUploadModal={handleShowUploadModal}
              emptyMessage="No hay documentos disponibles para esta reclamación"
            />
          )}
        </S.DocumentsSection>
      )}

      {/* Modal para subir archivo */}
      {showUploadModal && uploadingDocument && (
        <S.ModalOverlay onClick={() => setShowUploadModal(false)}>
          <S.Modal onClick={(e) => e.stopPropagation()}>
            <S.ModalHeader>
              <S.ModalTitle>Subir nuevo documento</S.ModalTitle>
              <S.CloseButton onClick={() => setShowUploadModal(false)}>
                <Close />
              </S.CloseButton>
            </S.ModalHeader>

            <S.ModalContent>
              <p style={{ marginBottom: "16px", color: "#6b7280" }}>
                Documento: <strong>{uploadingDocument.documento}</strong>
              </p>

              <FileUploader
                onFileSelect={handleFileUpload}
                currentFile={selectedFile ?? undefined}
              />
            </S.ModalContent>
          </S.Modal>
        </S.ModalOverlay>
      )}
    </S.Container>
  );
};

export default DocumentValidation;
