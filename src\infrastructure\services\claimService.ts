import axios from "@/core/axios";
import { Claim } from "@/hooks/useClaim";
import { APIClaimResponse, ClaimQueryParams, ClaimDocumentsResponse } from "@/types/claims";

export class ClaimService {
  private baseUrl = "v1/claims";

  async getDocuments() {
    const res = await axios.get(`${this.baseUrl}/documents/required`);

    return res.data as Claim[];
  }

  async getClaims(params?: ClaimQueryParams): Promise<APIClaimResponse> {
    const res = await axios.get(`${this.baseUrl}/me`, { params });
    return res.data as APIClaimResponse;
  }

  async remainingCoverage(
    id: string
  ): Promise<{ idPoliza: number; montoAsegurado: number }> {
    const res = await axios.post(`${this.baseUrl}/remaining-coverage`, {
      idPoliza: id,
    });

    return res.data;
  }

  /**
   * Obtiene todos los documentos de una reclamación específica
   * @param claimId ID de la reclamación
   * @returns Respuesta completa con documentos de la reclamación
   */
  async getClaimDocuments(claimId: string): Promise<ClaimDocumentsResponse> {
    const res = await axios.get(`${this.baseUrl}/${claimId}/documents`);
    return res.data as ClaimDocumentsResponse;
  }
}
